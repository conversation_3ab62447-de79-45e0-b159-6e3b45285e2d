cmake_minimum_required(VERSION 3.16)
project(HVIS_CudaProcessing LANGUAGES CXX)

# Exit on non-Windows
if(NOT MSVC)
    message(ERROR "CUDA processing module only supports MSVC")
endif()

# Set policy to avoid duplicate target errors
if(POLICY CMP0002)
    cmake_policy(SET CMP0002 NEW)
endif()

# Enable CUDA
enable_language(CUDA)

set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set consistent output directories
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Set CUDA flags
set(CMAKE_CUDA_ARCHITECTURES 75)
set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} -arch=sm_75")  # Adjust sm_75 to match your GPU architecture
set(CMAKE_CUDA_STANDARD 14)
set(CMAKE_CUDA_STANDARD_REQUIRED ON)

# Set OpenCV path (replace with your actual OpenCV installation path)
set(OpenCV_DIR "C:/Users/<USER>/repos/opencv/build" CACHE PATH "Path to OpenCV installation")
list(APPEND CMAKE_PREFIX_PATH ${OpenCV_DIR})

# Find OpenCV
find_package(OpenCV REQUIRED)
message(STATUS "Found OpenCV: ${OpenCV_VERSION}")

# Find packages using Conan
find_package(spdlog REQUIRED)
message(STATUS "Found spdlog: ${spdlog_VERSION}")

# Find GTest package
find_package(GTest REQUIRED)
message(STATUS "Found GTest: ${GTest_VERSION}")

# Automatically find all source and header files
file(GLOB_RECURSE SOURCES CONFIGURE_DEPENDS "src/*.cpp" "src/*.cu")
file(GLOB_RECURSE HEADERS CONFIGURE_DEPENDS "include/*.h" "*.h" "include/*.cuh" "*.cuh")

# Define the library
add_library(CudaProcessing SHARED
  ${SOURCES}
  ${HEADERS}
)

# Set properties for CUDA library
set_target_properties(CudaProcessing PROPERTIES 
    CUDA_SEPARABLE_COMPILATION ON
    POSITION_INDEPENDENT_CODE ON
    CUDA_RESOLVE_DEVICE_SYMBOLS ON
    LINKER_LANGUAGE CUDA
    WINDOWS_EXPORT_ALL_SYMBOLS ON
)

target_compile_definitions(CudaProcessing PRIVATE CUDAPROCESSING_LIBRARY)

# Link against spdlog and OpenCV
target_link_libraries(CudaProcessing PUBLIC
    spdlog::spdlog
    ${OpenCV_LIBS}
    ${CUDA_RUNTIME_LIBRARY}
)
target_include_directories(CudaProcessing PUBLIC
    "${CMAKE_CURRENT_SOURCE_DIR}/include"
    "C:/Program Files/Euresys/eGrabber/include"
    ${OpenCV_INCLUDE_DIRS}
)

# Build options - default to building just the library
option(BUILD_APP "Build the application" ON)
option(BUILD_TESTS "Build the tests" OFF)
option(BUILD_DOCS "Build documentation" OFF)

# Add the executable if requested
if(BUILD_APP)
  add_executable(CudaProcessingApp main.cpp)
  
  # Add dependency to ensure correct build order
  add_dependencies(CudaProcessingApp CudaProcessing)
  
  target_link_libraries(CudaProcessingApp PRIVATE
    CudaProcessing
    ${OpenCV_LIBS}
    ${CUDA_RUNTIME_LIBRARY}
    cuda
    cudart
  )
  target_include_directories(CudaProcessingApp PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/include)
endif()

# Add tests if requested
if(BUILD_TESTS)
  # Enable testing
  enable_testing()
  
  # Add tests directory
  add_subdirectory(tests)
endif()

# Add Doxygen documentation if requested
if(BUILD_DOCS)
  # Find Doxygen package
  find_package(Doxygen REQUIRED)
  
  # Set Doxygen configuration variables
  set(DOXYGEN_OUTPUT_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/docs)
  set(DOXYGEN_PROJECT_NAME "HVIS Cuda Processing")
  set(DOXYGEN_PROJECT_BRIEF "Cuda Processing library for HVIS project")
  set(DOXYGEN_EXTRACT_ALL YES)
  set(DOXYGEN_EXTRACT_PRIVATE YES)
  set(DOXYGEN_EXTRACT_PACKAGE YES)
  set(DOXYGEN_EXTRACT_STATIC YES)
  set(DOXYGEN_EXTRACT_LOCAL_CLASSES YES)
  set(DOXYGEN_RECURSIVE YES)
  set(DOXYGEN_USE_MDFILE_AS_MAINPAGE README.md)
  set(DOXYGEN_GENERATE_HTML YES)
  set(DOXYGEN_GENERATE_LATEX NO)
  
  # Add Doxygen target
  doxygen_add_docs(docs
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${CMAKE_CURRENT_SOURCE_DIR}/README.md
    COMMENT "Generating API documentation with Doxygen"
  )
endif()