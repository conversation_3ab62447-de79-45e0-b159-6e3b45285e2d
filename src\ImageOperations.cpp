#include "ImageOperations.h"
#include <iostream>


std::vector<cv::cuda::GpuMat> splitImageCUDA(uint8_t* gpuPtr, int width, int height, int channels, int tileWidth, int tileHeight)
{
    std::cout << "Calling split image CUDA" << std::endl;
    std::vector<cv::cuda::GpuMat> tiles;

    // Wrap the raw GPU pointer in a GpuMat
    cv::cuda::GpuMat d_image(height, width, CV_8UC(channels), gpuPtr);

    int numTilesX = width / tileWidth;
    int numTilesY = height / tileHeight;

    for (int row = 0; row < numTilesY; ++row)
    {
        for (int col = 0; col < numTilesX; ++col)
        {
            cv::Rect roi(col * tileWidth, row * tileHeight, tileWidth, tileHeight);
            tiles.push_back(d_image(roi));
        }
    }

    return tiles;
}