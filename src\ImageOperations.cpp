#include "ImageOperations.h"
#include <iostream>


std::vector<cv::cuda::GpuMat> splitImageCUDA(uint8_t* gpuPtr, int width, int height, int channels, int tileWidth, int tileHeight)
{
    std::vector<cv::cuda::GpuMat> tiles;

    std::cout << "splitImageCUDA called with: " << width << "x" << height << " channels: " << channels << std::endl;

    // Wrap the raw GPU pointer in a GpuMat
    cv::cuda::GpuMat d_image(height, width, CV_8UC(channels), gpuPtr);

    std::cout << "Created GpuMat: " << d_image.cols << "x" << d_image.rows << " channels: " << d_image.channels() << std::endl;
    std::cout << "GpuMat step: " << d_image.step << " elemSize: " << d_image.elemSize() << std::endl;

    // Show the image for sanity check
    cv::Mat h_image;
    d_image.download(h_image);

    std::cout << "Downloaded Mat: " << h_image.cols << "x" << h_image.rows << " channels: " << h_image.channels() << std::endl;
    std::cout << "Mat type: " << h_image.type() << " (CV_8UC3=" << CV_8UC3 << ")" << std::endl;

    cv::imshow("Image", h_image);
    cv::waitKey(0);

    int numTilesX = width / tileWidth;
    int numTilesY = height / tileHeight;

    for (int row = 0; row < numTilesY; ++row)
    {
        for (int col = 0; col < numTilesX; ++col)
        {
            // show every tile as a sanity check
            cv::cuda::GpuMat tile = d_image(cv::Rect(col * tileWidth, row * tileHeight, tileWidth, tileHeight));
            cv::Mat h_tile;
            tile.download(h_tile);
            cv::imshow("Tile", h_tile);
            cv::waitKey(0);

            cv::Rect roi(col * tileWidth, row * tileHeight, tileWidth, tileHeight);
            tiles.push_back(d_image(roi));
        }
    }

    // Save a random tile
    cv::cuda::GpuMat randomTile = tiles[4];
    cv::Mat h_randomTile;
    randomTile.download(h_randomTile);
    cv::imwrite("C:/Users/<USER>/repos/HVIS_CudaProcessing/test_images/randomTile.jpg", h_randomTile);

    return tiles;
}