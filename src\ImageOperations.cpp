#include "ImageOperations.h"
#include <iostream>


std::vector<cv::cuda::GpuMat> splitImageCUDA(uint8_t* gpuPtr, int width, int height, int channels, int tileWidth, int tileHeight)
{
    std::vector<cv::cuda::GpuMat> tiles;

    // Wrap the raw GPU pointer in a GpuMat
    cv::cuda::GpuMat d_image(height, width, CV_8UC(channels), gpuPtr);

    // Show the image for sanity check
    cv::Mat h_image;
    d_image.download(h_image);
    cv::imshow("Image", h_image);
    cv::waitKey(0);

    int numTilesX = width / tileWidth;
    int numTilesY = height / tileHeight;

    for (int row = 0; row < numTilesY; ++row)
    {
        for (int col = 0; col < numTilesX; ++col)
        {
            // show every tile as a sanity check
            cv::cuda::GpuMat tile = d_image(cv::Rect(col * tileWidth, row * tileHeight, tileWidth, tileHeight));
            cv::Mat h_tile;
            tile.download(h_tile);
            cv::imshow("Tile", h_tile);
            cv::waitKey(0);

            cv::Rect roi(col * tileWidth, row * tileHeight, tileWidth, tileHeight);
            tiles.push_back(d_image(roi));
        }
    }

    // Save a random tile
    cv::cuda::GpuMat randomTile = tiles[4];
    cv::Mat h_randomTile;
    randomTile.download(h_randomTile);
    cv::imwrite("C:/Users/<USER>/repos/HVIS_CudaProcessing/test_images/randomTile.jpg", h_randomTile);

    return tiles;
}