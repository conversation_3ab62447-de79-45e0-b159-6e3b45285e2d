#include <iostream>
#include <tuple>
#include "ImageCropping.cuh"
#include "ImageOperations.h"

#include "benchmark.h"


int main()
{
    // Load the image
    cv::Mat image = cv::imread("C:/Users/<USER>/repos/HVIS_CudaProcessing/test_images/test8.1.jpeg");

    // Check if image was loaded successfully
    if (image.empty()) {
        std::cerr << "Error: Could not load image!" << std::endl;
        return -1;
    }

    // Print actual image dimensions
    std::cout << "Image dimensions: " << image.cols << "x" << image.rows << " channels: " << image.channels() << std::endl;

    // Move the image to the GPU
    cv::cuda::GpuMat d_image;
    d_image.upload(image);

    // Extract the pointer to the GPU memory
    uint8_t* gpuPtr = d_image.ptr();

    // Check if image dimensions are compatible with tile size
    int numTilesX = image.cols / 640;
    int numTilesY = image.rows / 640;
    std::cout << "Will create " << numTilesX << "x" << numTilesY << " = " << (numTilesX * numTilesY) << " tiles" << std::endl;

    if (numTilesX == 0 || numTilesY == 0) {
        std::cerr << "Warning: Image is smaller than tile size (640x640)!" << std::endl;
    }

    // Use actual image dimensions instead of hardcoded values
    std::ignore = benchmarkFunctionWithTiming(splitImageCUDA, 1, gpuPtr, image.cols, image.rows, image.channels(), 640, 640);

    return 0;
}


