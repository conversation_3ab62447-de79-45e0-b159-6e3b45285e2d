#include <iostream>
#include <tuple>
#include <functional>
#include "ImageCropping.cuh"
#include "ImageOperations.h"

#include "benchmark.h"


int main()
{
    // Load the image
    cv::Mat image = cv::imread("C:/Users/<USER>/repos/HVIS_CudaProcessing/test_images/test8.1.jpeg");

    // Check if image was loaded successfully
    if (image.empty()) {
        std::cerr << "Error: Could not load image!" << std::endl;
        return -1;
    }

    // Print actual image dimensions for verification
    std::cout << "Loaded image dimensions: " << image.cols << "x" << image.rows << " channels: " << image.channels() << std::endl;
    std::cout << "Image type: " << image.type() << " (CV_8UC3=" << CV_8UC3 << ")" << std::endl;
    std::cout << "Image step: " << image.step << " elemSize: " << image.elemSize() << std::endl;
    std::cout << "Expected dimensions: 19073x5000" << std::endl;

    // Show original image (resized for display)
    cv::Mat display_image;
    cv::resize(image, display_image, cv::Size(800, 600));
    cv::imshow("Original Image (resized)", display_image);
    cv::waitKey(0);

    // Move the image to the GPU
    cv::cuda::GpuMat d_image;
    d_image.upload(image);

    std::cout << "GPU image step: " << d_image.step << " elemSize: " << d_image.elemSize() << std::endl;

    // Pass the GpuMat directly instead of extracting pointer
    std::ignore = benchmarkFunctionWithTiming(splitImageCUDA, 1, std::cref(d_image), 640, 640);

    return 0;
}


