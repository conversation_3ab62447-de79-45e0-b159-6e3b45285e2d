#include <iostream>
#include <tuple>
#include "ImageCropping.cuh"
#include "ImageOperations.h"

#include "benchmark.h"


int main()
{
    // Create an empty image in RGB with 19073x5000 pixels size
    cv::Mat image = cv::imread("C:/Users/<USER>/repos/HVIS_CudaProcessing/test_images/test8.1.jpeg");

    // Move the image to the GPU
    cv::cuda::GpuMat d_image;
    d_image.upload(image);

    // Extract the pointer to the GPU memory
    uint8_t* gpuPtr = d_image.ptr();

    benchmarkFunctionWithTiming(splitImageCUDA, 1, gpuPtr, 19073, 5000, 3, 640, 640);
    // splitImageCUDA(gpuPtr, 19073, 5000, 3, 640, 640);

    return 0;
}


