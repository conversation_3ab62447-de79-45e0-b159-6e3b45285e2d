#include <iostream>
#include <tuple>
#include <functional>
#include "ImageCropping.cuh"
#include "ImageOperations.h"

#include "benchmark.h"


int main()
{
    // Load the image
    cv::Mat image = cv::imread("C:/Users/<USER>/repos/HVIS_CudaProcessing/test_images/test8.1.jpeg");

    // Show the image scalated to 800x600
    cv::Mat display_image;
    cv::resize(image, display_image, cv::Size(800, 600));
    cv::imshow("Original Image (resized)", display_image);
    cv::waitKey(0);

    // Move the image to the GPU
    cv::cuda::GpuMat d_image;
    d_image.upload(image);
    
    // Show the image again
    cv::Mat h_image, scalated;
    d_image.download(h_image);
    cv::resize(h_image, scalated, cv::Size(800, 600));
    cv::imshow("Original Image (downloaded)", scalated);
    cv::waitKey(0);

    // Extract the pointer
    uint8_t* gpuPtr = d_image.ptr();

    // Print the pointer value
    std::cout << "GPU Pointer: " << static_cast<void*>(gpuPtr) << std::endl;

    // Validate the data inside the pointer
    cv::Mat h_image_from_ptr(d_image.rows, d_image.cols, CV_8UC3, gpuPtr, d_image.step);
    cv::imshow("Image from GPU Pointer", h_image_from_ptr);
    cv::waitKey(0); 

    // Use the pointer-based function (keeping your original signature)
    // benchmarkFunctionWithTiming(splitImageCUDA, 1, gpuPtr, image.cols, image.rows, image.channels(), 640, 640);

    return 0;
}


