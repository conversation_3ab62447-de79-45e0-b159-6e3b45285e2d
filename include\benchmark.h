#include <iostream>
#include <functional>
#include <chrono>


template<typename F, typename... Args>
void benchmarkFunctionWithTiming(F func, int times, Args&&... args)
{ 
    std::vector<long long> durations;
    durations.reserve(times);

    for (int i = 0; i < times; i++)
    {
        auto start = std::chrono::high_resolution_clock::now();
        func(std::forward<Args>(args)...);
        auto end = std::chrono::high_resolution_clock::now();

        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        durations.push_back(duration.count());
    }

    // Calculate statistics
    long long total = 0;
    long long min_time = durations[0];
    long long max_time = durations[0];
    
    for (const auto& duration : durations) {
        total += duration;
        min_time = std::min(min_time, duration);
        max_time = std::max(max_time, duration);
    }
    
    double average = static_cast<double>(total) / times;
    
    // Print results
    std::cout << "\nBenchmark Results (" << times << " iterations):" << std::endl;
    std::cout << "Average time: " << average << " ms" << std::endl;
    std::cout << "Minimum time: " << min_time << " ms" << std::endl;
    std::cout << "Maximum time: " << max_time << " ms" << std::endl;
    std::cout << "Total time: " << total << " ms" << std::endl;
}