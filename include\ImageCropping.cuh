#ifndef IMAGE_CROPPING_CUH
#define IMAGE_CROPPING_CUH

#include <cuda_runtime.h>

// CUDA kernel declaration
__global__ void cropImageKernel(
    const unsigned char* input,
    unsigned char* output,
    int inputWidth,
    int inputHeight,
    int regionWidth,
    int regionHeight,
    int numCols,
    int outputPitch
);

// CUDA kernel launch function declaration
extern "C" void launchCropKernel(
    const unsigned char* d_input,
    unsigned char* d_output,
    int inputWidth,
    int inputHeight,
    int regionWidth,
    int regionHeight,
    int numCols
);

#endif // IMAGE_CROPPING_CUH